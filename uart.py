import serial
import time

# 配置串口参数
SERIAL_PORT = 'COM3'      
BAUDRATE = 115200
TIMEOUT = 1        

# 要发送的数据（可以是 bytes 或 ASCII）
SEND_DATA = b'\x01\x02\x03\x04'  # 示例：发送4字节数据
# 或者发送 ASCII 字符串，如：
# SEND_DATA = "Hello UART\n".encode()

def uart_send_receive():
    try:
        # 打开串口
        ser = serial.Serial(
            port=SERIAL_PORT,
            baudrate=BAUDRATE,
            timeout=TIMEOUT,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            bytesize=serial.EIGHTBITS
        )
        
        if ser.is_open:
            print(f"[INFO] 串口 {SERIAL_PORT} 已打开，准备发送数据...")

            # 发送数据
            ser.write(SEND_DATA)
            print(f"[TX] 发送数据: {SEND_DATA}")

            # 等待设备响应
            time.sleep(0.1)  # 视情况延迟

            # 接收回显/响应
            response = ser.read(64)  # 最多读取64字节
            print(f"[RX] 接收到数据: {response}")

            # 可选：循环监听
            # while True:
            #     data = ser.readline()
            #     if data:
            #         print(f"[RX] {data}")

            ser.close()
        else:
            print(f"[ERROR] 无法打开串口 {SERIAL_PORT}")

    except serial.SerialException as e:
        print(f"[EXCEPTION] 串口错误: {e}")

if __name__ == "__main__":
    uart_send_receive()
